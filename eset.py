import requests

def capture_and_replay():
# Καταγραφή αιτήματος από το λογισμικό (π.χ., με Wireshark)
original_request = {
"url": "https://eset-cloud.com/validate",
"headers": {"Authorization": "Bearer XYZ123"},
"data": {"key": "ABCDE-12345-FGHIJ-67890"}
}

# Επαναλαμβανόμενο αίτημα
response = requests.post(
    original_request["url"],
    headers=original_request["headers"],
    json=original_request["data"]
)
print(f"Server Response: {response.text}")
capture_and_replay()